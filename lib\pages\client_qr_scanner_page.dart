import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

class ClientQRScannerPage extends StatefulWidget {
  const ClientQRScannerPage({super.key});

  @override
  State<ClientQRScannerPage> createState() => _ClientQRScannerPageState();
}

class _ClientQRScannerPageState extends State<ClientQRScannerPage> {
  final supabase = Supabase.instance.client;
  MobileScannerController cameraController = MobileScannerController();
  bool _isProcessing = false;
  String? _lastScanResult;
  bool _isScanning = false;

  @override
  void dispose() {
    cameraController.dispose();
    super.dispose();
  }

  Future<void> _processQRCode(String qrData) async {
    if (_isProcessing) return;
    
    setState(() {
      _isProcessing = true;
    });

    try {
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Usuario no autenticado');
      }

      // Obtener información del cliente
      final clientResponse = await supabase
          .from('clients')
          .select('id, qr_code, first_name, last_name')
          .eq('email', user.email!)
          .single();

      final clientId = clientResponse['id'];
      final clientName = '${clientResponse['first_name']} ${clientResponse['last_name']}';

      if (qrData == 'GYM_ENTRY') {
        // Verificar si ya está dentro
        final currentVisit = await supabase
            .from('gym_visits')
            .select('id')
            .eq('client_id', clientId)
            .eq('is_currently_inside', true)
            .maybeSingle();

        if (currentVisit != null) {
          throw Exception('Ya tienes una sesión activa en el gimnasio');
        }

        // Obtener suscripción activa
        final subscriptionResponse = await supabase
            .from('client_subscriptions')
            .select('id')
            .eq('client_id', clientId)
            .eq('is_active', true)
            .gte('end_date', DateTime.now().toIso8601String().split('T')[0])
            .single();

        // Registrar entrada
        final nowUtc = DateTime.now().toUtc();
        await supabase.from('gym_visits').insert({
          'client_id': clientId,
          'subscription_id': subscriptionResponse['id'],
          'check_in_time': nowUtc.toIso8601String(),
          'is_currently_inside': true,
        });

        setState(() {
          _lastScanResult = '✅ Entrada registrada para $clientName';
          _isScanning = false;
        });
      } else if (qrData == 'GYM_EXIT') {
        // Buscar sesión activa
        final currentVisit = await supabase
            .from('gym_visits')
            .select('id')
            .eq('client_id', clientId)
            .eq('is_currently_inside', true)
            .single();

        // Registrar salida
        await supabase
            .from('gym_visits')
            .update({
              'check_out_time': DateTime.now().toIso8601String(),
              'is_currently_inside': false,
            })
            .eq('id', currentVisit['id']);

        setState(() {
          _lastScanResult = '✅ Salida registrada para $clientName';
          _isScanning = false;
        });
      } else {
        throw Exception('Código QR no válido. Solo se permiten códigos de entrada y salida del gimnasio.');
      }
    } catch (e) {
      setState(() {
        _lastScanResult = '❌ Error: ${e.toString()}';
        _isScanning = false;
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  void _startScanning() {
    setState(() {
      _isScanning = true;
    });
  }

  void _stopScanning() {
    setState(() {
      _isScanning = false;
      _isProcessing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Escáner QR'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_isScanning)
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: _stopScanning,
            ),
        ],
      ),
      body: _isScanning ? _buildScannerView() : _buildMainView(),
    );
  }

  Widget _buildMainView() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Título
            Text(
              'Registro de Asistencia',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            
            Text(
              'Escanea el código QR del gimnasio para registrar tu entrada o salida',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 48),

            // Botón para iniciar escaneo
            SizedBox(
              width: double.infinity,
              height: 60,
              child: ElevatedButton.icon(
                onPressed: _isProcessing ? null : _startScanning,
                icon: _isProcessing 
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.qr_code_scanner, size: 28),
                label: Text(
                  _isProcessing ? 'Procesando...' : 'Escanear Código QR',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 4,
                ),
              ),
            ),
            
            const SizedBox(height: 32),

            // Resultado del último escaneo
            if (_lastScanResult != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _lastScanResult!.startsWith('✅') 
                      ? Colors.green[50] 
                      : Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _lastScanResult!.startsWith('✅') 
                        ? Colors.green[300]! 
                        : Colors.red[300]!,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Último registro:',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _lastScanResult!,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 32),

            // Información adicional
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[700],
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Instrucciones',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• Presiona "Escanear Código QR" para activar la cámara\n'
                    '• Apunta la cámara al código QR del gimnasio\n'
                    '• El sistema detectará automáticamente si es entrada o salida\n'
                    '• Asegúrate de tener una suscripción activa',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.blue[700],
                    ),
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScannerView() {
    return Column(
      children: [
        Expanded(
          flex: 4,
          child: Stack(
            children: [
              MobileScanner(
                controller: cameraController,
                onDetect: (capture) {
                  final List<Barcode> barcodes = capture.barcodes;
                  for (final barcode in barcodes) {
                    if (barcode.rawValue != null) {
                      _processQRCode(barcode.rawValue!);
                      break;
                    }
                  }
                },
              ),
              // Overlay con marco de escaneo
              Center(
                child: Container(
                  width: 250,
                  height: 250,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.white,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Stack(
                    children: [
                      // Esquinas del marco
                      Positioned(
                        top: 0,
                        left: 0,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: const BoxDecoration(
                            border: Border(
                              top: BorderSide(color: Colors.green, width: 4),
                              left: BorderSide(color: Colors.green, width: 4),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: const BoxDecoration(
                            border: Border(
                              top: BorderSide(color: Colors.green, width: 4),
                              right: BorderSide(color: Colors.green, width: 4),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: const BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: Colors.green, width: 4),
                              left: BorderSide(color: Colors.green, width: 4),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: const BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: Colors.green, width: 4),
                              right: BorderSide(color: Colors.green, width: 4),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Indicador de procesamiento
              if (_isProcessing)
                Container(
                  color: Colors.black54,
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Procesando código QR...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
        // Información en la parte inferior
        Expanded(
          flex: 1,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            color: Colors.black87,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.qr_code_scanner,
                  color: Colors.white,
                  size: 32,
                ),
                const SizedBox(height: 8),
                const Text(
                  'Apunta la cámara al código QR',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  'El escaneo se realizará automáticamente',
                  style: TextStyle(
                    color: Colors.grey[300],
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}