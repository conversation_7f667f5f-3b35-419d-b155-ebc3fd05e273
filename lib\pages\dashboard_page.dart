import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'client_detail_page.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  final supabase = Supabase.instance.client;
  List<Map<String, dynamic>> _clientsInGym = [];
  bool _isLoading = true;
  RealtimeChannel? _realtimeChannel;

  @override
  void initState() {
    super.initState();
    _loadClientsInGym();
    _setupRealtimeSubscription();
  }

  @override
  void dispose() {
    _realtimeChannel?.unsubscribe();
    super.dispose();
  }

  void _setupRealtimeSubscription() {
    _realtimeChannel = supabase
        .channel('gym_visits_realtime')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'gym_visits',
          callback: (payload) {
            // Recargar datos cuando hay cambios en gym_visits
            _loadClientsInGym();
          },
        )
        .subscribe();
  }

  Future<void> _loadClientsInGym() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final response = await supabase
          .from('gym_visits')
          .select('''
            id,
            check_in_time,
            client_id,
            clients!inner(
              id,
              first_name,
              last_name,
              email,
              phone,
              qr_code
            ),
            client_subscriptions!inner(
              id,
              start_date,
              end_date,
              classes_used,
              subscription_types!inner(
                id,
                name,
                price,
                duration_months,
                description,
                schedule_restrictions,
                class_limit
              )
            )
          ''')
          .eq('is_currently_inside', true)
          .order('check_in_time', ascending: false);

      setState(() {
        _clientsInGym = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al cargar clientes: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatCheckInTime(String checkInTime) {
  // 1) Quitamos cualquier indicación de zona: 'Z' o '+hh:mm' o '-hh:mm'
  final localString = checkInTime.replaceFirst(
    RegExp(r'(?:Z|[+-]\d{2}:\d{2})$'),
    '',
  );

  // 2) Parseamos como hora local
  final checkInLocal = DateTime.parse(localString);

  // 3) Ahora calculamos con la hora local
  final nowLocal = DateTime.now();
  final diff     = nowLocal.difference(checkInLocal);

  if (diff.inMinutes < 1) {
    return 'Ahora mismo';
  } else if (diff.inMinutes < 60) {
    return 'Hace ${diff.inMinutes} min';
  } else if (diff.inHours < 24) {
    final h = diff.inHours;
    final m = diff.inMinutes % 60;               // \(m = \mathrm{diff.inMinutes}\bmod 60\)
    return 'Hace ${h}h ${m}min';
  } else {
    final d  = checkInLocal.day;
    final mo = checkInLocal.month;
    final hh = checkInLocal.hour;
    final mm = checkInLocal.minute.toString().padLeft(2, '0');
    return '$d/$mo $hh:$mm';
  }
}

  String _getSubscriptionStatus(Map<String, dynamic> subscription) {
    final endDate = DateTime.parse(subscription['end_date']);
    final now = DateTime.now();
    final daysLeft = endDate.difference(now).inDays;

    if (daysLeft < 0) {
      return 'Vencida';
    } else if (daysLeft == 0) {
      return 'Vence hoy';
    } else if (daysLeft <= 7) {
      return 'Vence en $daysLeft días';
    } else {
      return 'Activa';
    }
  }

  Color _getSubscriptionStatusColor(Map<String, dynamic> subscription) {
    final endDate = DateTime.parse(subscription['end_date']);
    final now = DateTime.now();
    final daysLeft = endDate.difference(now).inDays;

    if (daysLeft < 0) {
      return Colors.red;
    } else if (daysLeft <= 7) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Header con estadísticas
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade600, Colors.blue.shade800],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                const Text(
                  'Clientes en el Gimnasio',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _isLoading ? '...' : '${_clientsInGym.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Text(
                  'personas actualmente',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                ),
              ],
            ),
          ),

          // Lista de clientes
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _clientsInGym.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.fitness_center,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'No hay clientes en el gimnasio',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Los clientes aparecerán aquí cuando hagan check-in',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : RefreshIndicator(
                    onRefresh: _loadClientsInGym,
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _clientsInGym.length,
                      itemBuilder: (context, index) {
                        final visit = _clientsInGym[index];
                        final client = visit['clients'];
                        final subscription = visit['client_subscriptions'];
                        final subscriptionType =
                            subscription['subscription_types'];

                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          elevation: 4,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ClientDetailPage(
                                    clientId: client['id'].toString(),
                                    clientName:
                                        '${client['first_name']} ${client['last_name']}',
                                    clientEmail: client['email'],
                                  ),
                                ),
                              );
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Header del cliente
                                  Row(
                                    children: [
                                      CircleAvatar(
                                        backgroundColor: Colors.blue.shade100,
                                        child: Text(
                                          '${client['first_name'][0]}${client['last_name'][0]}',
                                          style: TextStyle(
                                            color: Colors.blue.shade800,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              '${client['first_name']} ${client['last_name']}',
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            if (client['email'] != null)
                                              Text(
                                                client['email'],
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.grey.shade600,
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          Text(
                                            _formatCheckInTime(
                                              visit['check_in_time'],
                                            ),
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.green.shade100,
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Text(
                                              'Dentro',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.green.shade800,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 12),
                                  const Divider(height: 1),
                                  const SizedBox(height: 12),

                                  // Información de la suscripción
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.card_membership,
                                        size: 20,
                                        color: Colors.blue.shade600,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              subscriptionType['name'],
                                              style: const TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            Text(
                                              '\$${subscriptionType['price']} - ${subscriptionType['description']}',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey.shade600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: _getSubscriptionStatusColor(
                                            subscription,
                                          ).withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Text(
                                          _getSubscriptionStatus(subscription),
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: _getSubscriptionStatusColor(
                                              subscription,
                                            ),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  // Información adicional de clases (si aplica)
                                  if (subscriptionType['class_limit'] !=
                                      null) ...[
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.fitness_center,
                                          size: 16,
                                          color: Colors.orange.shade600,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Clases usadas: ${subscription['classes_used']}/${subscriptionType['class_limit']}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade700,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
