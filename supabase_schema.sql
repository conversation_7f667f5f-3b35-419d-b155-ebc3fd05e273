-- Esquema de base de datos para GymTracker
-- Este archivo contiene las tablas necesarias para el sistema de gimnasio

-- Tabla de usuarios del sistema (administradores, encargados, recepcionistas)
-- La autenticación se maneja con Supabase Auth, esta tabla solo almacena información adicional
CREATE TABLE system_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_user_id UUID UNIQUE NOT NULL, -- Referencia al usuario de Supabase Auth
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'manager', 'receptionist', 'client')),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de tipos de suscripciones
CREATE TABLE subscription_types (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    duration_months INTEGER NOT NULL,
    description TEXT,
    schedule_restrictions TEXT, -- Para horarios específicos como matutino/vespertino
    class_limit INTEGER, -- Límite de clases por mes (null = ilimitado)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insertar los tipos de suscripción de la imagen
INSERT INTO subscription_types (name, price, duration_months, description, schedule_restrictions, class_limit) VALUES
('Mensualidad', 1400.00, 1, 'Clases todo el mes', null, null),
('Matutino', 1200.00, 1, 'Clases diarias solo por la mañana desde las 6:00am a las 10:00am', 'morning', null),
('Vespertino', 1200.00, 1, 'Clases diarias solo por la tarde desde las 2:00pm a las 6:00pm de lunes a viernes', 'evening', null),
('12 clases al mes', 1000.00, 1, '12 clases en el mes, tú eliges que días venir y el horario es el principal desde las 6:00am a las 10:00pm, las clases solo se utilizan hasta que venza tu mensualidad', null, 12);

-- Tabla de clientes
CREATE TABLE clients (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    date_of_birth DATE,
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    qr_code VARCHAR(255) UNIQUE NOT NULL, -- Código QR único para cada cliente
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de suscripciones activas de clientes
CREATE TABLE client_subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    subscription_type_id UUID NOT NULL REFERENCES subscription_types(id),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    classes_used INTEGER DEFAULT 0, -- Para suscripciones con límite de clases
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de check-ins/check-outs (para tiempo real)
CREATE TABLE gym_visits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    subscription_id UUID NOT NULL REFERENCES client_subscriptions(id),
    check_in_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    check_out_time TIMESTAMP WITH TIME ZONE,
    visit_type VARCHAR(20) DEFAULT 'class', -- 'class', 'open_gym', etc.
    is_currently_inside BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para optimizar consultas en tiempo real
CREATE INDEX idx_gym_visits_currently_inside ON gym_visits(is_currently_inside) WHERE is_currently_inside = true;
CREATE INDEX idx_gym_visits_client_id ON gym_visits(client_id);
CREATE INDEX idx_gym_visits_check_in_time ON gym_visits(check_in_time);
CREATE INDEX idx_client_subscriptions_active ON client_subscriptions(is_active) WHERE is_active = true;
CREATE INDEX idx_clients_qr_code ON clients(qr_code);

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para actualizar updated_at
CREATE TRIGGER update_system_users_updated_at BEFORE UPDATE ON system_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscription_types_updated_at BEFORE UPDATE ON subscription_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_client_subscriptions_updated_at BEFORE UPDATE ON client_subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Función para validar horarios de suscripción
CREATE OR REPLACE FUNCTION validate_subscription_schedule()
RETURNS TRIGGER AS $$
DECLARE
    schedule_restriction TEXT;
    current_hour INTEGER;
BEGIN
    -- Obtener la restricción de horario de la suscripción
    SELECT st.schedule_restrictions INTO schedule_restriction
    FROM client_subscriptions cs
    JOIN subscription_types st ON cs.subscription_type_id = st.id
    WHERE cs.id = NEW.subscription_id;
    
    -- Obtener la hora actual
    current_hour := EXTRACT(HOUR FROM NEW.check_in_time);
    
    -- Validar horarios
    IF schedule_restriction = 'morning' AND (current_hour < 6 OR current_hour >= 10) THEN
        RAISE EXCEPTION 'Esta suscripción solo permite acceso de 6:00 AM a 10:00 AM';
    END IF;
    
    IF schedule_restriction = 'evening' AND (current_hour < 14 OR current_hour >= 18) THEN
        RAISE EXCEPTION 'Esta suscripción solo permite acceso de 2:00 PM a 6:00 PM';
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para validar horarios en check-in
CREATE TRIGGER validate_schedule_on_checkin BEFORE INSERT ON gym_visits FOR EACH ROW EXECUTE FUNCTION validate_subscription_schedule();

-- Función para incrementar clases usadas
CREATE OR REPLACE FUNCTION increment_classes_used()
RETURNS TRIGGER AS $$
BEGIN
    -- Incrementar clases usadas solo si la suscripción tiene límite
    UPDATE client_subscriptions 
    SET classes_used = classes_used + 1
    WHERE id = NEW.subscription_id 
    AND id IN (
        SELECT cs.id 
        FROM client_subscriptions cs
        JOIN subscription_types st ON cs.subscription_type_id = st.id
        WHERE st.class_limit IS NOT NULL
    );
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para incrementar clases usadas en check-in
CREATE TRIGGER increment_classes_on_checkin AFTER INSERT ON gym_visits FOR EACH ROW EXECUTE FUNCTION increment_classes_used();

-- Vista para obtener clientes actualmente en el gimnasio
CREATE VIEW clients_currently_in_gym AS
SELECT 
    c.id,
    c.first_name,
    c.last_name,
    c.email,
    gv.check_in_time,
    st.name as subscription_type
FROM clients c
JOIN gym_visits gv ON c.id = gv.client_id
JOIN client_subscriptions cs ON gv.subscription_id = cs.id
JOIN subscription_types st ON cs.subscription_type_id = st.id
WHERE gv.is_currently_inside = true
ORDER BY gv.check_in_time DESC;

-- Habilitar Row Level Security (RLS) para tiempo real
ALTER TABLE system_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE gym_visits ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_types ENABLE ROW LEVEL SECURITY;

-- Habilitar Realtime para las tablas necesarias
ALTER PUBLICATION supabase_realtime ADD TABLE gym_visits;
ALTER PUBLICATION supabase_realtime ADD TABLE clients;
ALTER PUBLICATION supabase_realtime ADD TABLE client_subscriptions;
ALTER PUBLICATION supabase_realtime ADD TABLE subscription_types;

-- Políticas básicas (ajustar según necesidades de autenticación)
CREATE POLICY "Allow all operations" ON system_users FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON gym_visits FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON clients FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON client_subscriptions FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON subscription_types FOR ALL USING (true);

-- Tabla de productos de la tienda
CREATE TABLE store_products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    stock_quantity INTEGER DEFAULT 0,
    category VARCHAR(100),
    image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de carritos de compra
CREATE TABLE shopping_carts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES system_users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'submitted', 'cancelled')),
    total_amount DECIMAL(10,2) DEFAULT 0,
    submitted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de items del carrito
CREATE TABLE cart_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    cart_id UUID NOT NULL REFERENCES shopping_carts(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES store_products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para optimizar consultas de la tienda
CREATE INDEX idx_store_products_active ON store_products(is_active) WHERE is_active = true;
CREATE INDEX idx_store_products_category ON store_products(category);
CREATE INDEX idx_shopping_carts_user_status ON shopping_carts(user_id, status);
CREATE INDEX idx_cart_items_cart_id ON cart_items(cart_id);

-- Triggers para actualizar updated_at en tablas de la tienda
CREATE TRIGGER update_store_products_updated_at BEFORE UPDATE ON store_products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_shopping_carts_updated_at BEFORE UPDATE ON shopping_carts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cart_items_updated_at BEFORE UPDATE ON cart_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Función para calcular el total del carrito
CREATE OR REPLACE FUNCTION update_cart_total()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE shopping_carts 
    SET total_amount = (
        SELECT COALESCE(SUM(total_price), 0)
        FROM cart_items 
        WHERE cart_id = COALESCE(NEW.cart_id, OLD.cart_id)
    )
    WHERE id = COALESCE(NEW.cart_id, OLD.cart_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Triggers para actualizar el total del carrito
CREATE TRIGGER update_cart_total_on_insert AFTER INSERT ON cart_items FOR EACH ROW EXECUTE FUNCTION update_cart_total();
CREATE TRIGGER update_cart_total_on_update AFTER UPDATE ON cart_items FOR EACH ROW EXECUTE FUNCTION update_cart_total();
CREATE TRIGGER update_cart_total_on_delete AFTER DELETE ON cart_items FOR EACH ROW EXECUTE FUNCTION update_cart_total();

-- Habilitar RLS para las tablas de la tienda
ALTER TABLE store_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE shopping_carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;

-- Habilitar Realtime para las tablas de la tienda
ALTER PUBLICATION supabase_realtime ADD TABLE store_products;
ALTER PUBLICATION supabase_realtime ADD TABLE shopping_carts;
ALTER PUBLICATION supabase_realtime ADD TABLE cart_items;

-- Políticas para las tablas de la tienda
CREATE POLICY "Allow all operations" ON store_products FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON shopping_carts FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON cart_items FOR ALL USING (true);

-- Comentarios para documentación
COMMENT ON TABLE system_users IS 'Información adicional de usuarios del sistema (administradores, encargados y recepcionistas). La autenticación se maneja con Supabase Auth';
COMMENT ON TABLE subscription_types IS 'Tipos de suscripciones disponibles en el gimnasio';
COMMENT ON TABLE clients IS 'Información de los clientes del gimnasio';
COMMENT ON TABLE client_subscriptions IS 'Suscripciones activas de los clientes';
COMMENT ON TABLE gym_visits IS 'Registro de entradas y salidas del gimnasio para tiempo real';
COMMENT ON TABLE store_products IS 'Productos disponibles en la tienda del gimnasio';
COMMENT ON TABLE shopping_carts IS 'Carritos de compra de los usuarios';
COMMENT ON TABLE cart_items IS 'Items individuales dentro de los carritos de compra';
COMMENT ON VIEW clients_currently_in_gym IS 'Vista en tiempo real de clientes actualmente en el gimnasio';