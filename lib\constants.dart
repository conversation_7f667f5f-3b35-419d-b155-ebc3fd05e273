class AppConstants {
  static const String appTitle = 'Box 4 Life';
  
  // Navigation labels
  static const String qrLabel = 'QR';
  static const String subscriptionsLabel = 'Suscripciones';
  static const String storeLabel = 'Tienda';
  static const String dashboardLabel = 'Dashboard';
  
  // QR Page
  static const String qrPageTitle = 'Escanea el QR correspondiente';
  static const String entryLabel = 'Entrada';
  static const String exitLabel = 'Salida';
  
  // Page titles
  static const String subscriptionsPageTitle = 'Suscripciones';
  static const String storePageTitle = 'Página de Tienda';
  static const String dashboardPageTitle = 'Página de Dashboard';
  
  // Subscriptions Page
  static const String subscriptionsTitle = 'Planes de Suscripción';
  static const String subscriptionsSubtitle = 'Elige el plan que mejor se adapte a tus necesidades';
  static const String monthlyLabel = 'por mes';
  static const String selectPlanButton = 'Seleccionar Plan';
  static const String noSubscriptionsAvailable = 'No hay suscripciones disponibles';
  static const String loadingSubscriptions = 'Cargando suscripciones...';
  static const String errorLoadingSubscriptions = 'Error al cargar suscripciones';
  
  // Client Registration
  static const String clientRegistrationTitle = 'Registrar Cliente';
  static const String firstNameLabel = 'Nombre';
  static const String lastNameLabel = 'Apellido';
  static const String emailLabel = 'Correo Electrónico';
  static const String phoneLabel = 'Teléfono';
  static const String emergencyContactNameLabel = 'Contacto de Emergencia';
  static const String emergencyContactPhoneLabel = 'Teléfono de Emergencia';
  static const String dateOfBirthLabel = 'Fecha de Nacimiento';
  static const String registerClientButton = 'Registrar Cliente';
  static const String cancelButton = 'Cancelar';
  static const String requiredFieldError = 'Este campo es requerido';
  static const String invalidEmailError = 'Ingresa un correo válido';
  static const String clientRegisteredSuccess = 'Cliente registrado exitosamente';
  static const String clientRegistrationError = 'Error al registrar cliente';
  static const String subscriptionAssignedSuccess = 'Suscripción asignada exitosamente';
  static const String subscriptionAssignmentError = 'Error al asignar suscripción';
  
  // Subscription Features
  static const String unlimitedAccess = 'Acceso ilimitado';
  static const String morningSchedule = 'Horario matutino';
  static const String eveningSchedule = 'Horario vespertino';
  static const String classLimit = 'clases por mes';
  static const String mostPopular = 'Más Popular';
  static const String recommended = 'Recomendado';
}