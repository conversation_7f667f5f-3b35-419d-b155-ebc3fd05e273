import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants.dart';

// Enum para las pestañas de navegación
enum NavigationTab {
  qr,
  subscriptions,
  store,
  dashboard,
  users,
  scanner, // Para clientes
  profile, // Para clientes
}

// Extensión para obtener información de las pestañas
extension NavigationTabExtension on NavigationTab {
  String get label {
    switch (this) {
      case NavigationTab.qr:
        return AppConstants.qrLabel;
      case NavigationTab.subscriptions:
        return AppConstants.subscriptionsLabel;
      case NavigationTab.store:
        return AppConstants.storeLabel;
      case NavigationTab.dashboard:
        return AppConstants.dashboardLabel;
      case NavigationTab.users:
        return 'Usuarios';
      case NavigationTab.scanner:
        return 'Escáner';
      case NavigationTab.profile:
        return 'Perfil';
    }
  }

  IconData get iconData {
    switch (this) {
      case NavigationTab.qr:
        return Icons.qr_code;
      case NavigationTab.subscriptions:
        return Icons.card_membership;
      case NavigationTab.store:
        return Icons.store;
      case NavigationTab.dashboard:
        return Icons.dashboard;
      case NavigationTab.users:
        return Icons.people;
      case NavigationTab.scanner:
        return Icons.qr_code_scanner;
      case NavigationTab.profile:
        return Icons.person;
    }
  }

  String get iconName {
    switch (this) {
      case NavigationTab.qr:
        return 'qr_code';
      case NavigationTab.subscriptions:
        return 'card_membership';
      case NavigationTab.store:
        return 'store';
      case NavigationTab.dashboard:
        return 'dashboard';
      case NavigationTab.users:
        return 'people';
      case NavigationTab.scanner:
        return 'qr_code_scanner';
      case NavigationTab.profile:
        return 'person';
    }
  }
}

class UserService {
  final supabase = Supabase.instance.client;

  // Tipos de usuario
  static const String roleAdmin = 'admin';
  static const String roleManager = 'manager';
  static const String roleReceptionist = 'receptionist';
  static const String roleClient = 'client';

  // Obtener información del usuario actual
  Future<Map<String, dynamic>> getCurrentUserInfo() async {
    final user = supabase.auth.currentUser;
    if (user == null) {
      throw Exception('Usuario no autenticado');
    }

    try {
      // Buscar en system_users primero
      final systemUserResponse = await supabase
          .from('system_users')
          .select('*')
          .eq('auth_user_id', user.id)
          .maybeSingle();

      if (systemUserResponse != null) {
        return {
          'id': systemUserResponse['id'],
          'email': systemUserResponse['email'],
          'first_name': systemUserResponse['first_name'],
          'last_name': systemUserResponse['last_name'],
          'role': systemUserResponse['role'],
          'phone': systemUserResponse['phone'],
          'is_active': systemUserResponse['is_active'],
          'user_type': 'system', // admin, manager, receptionist
        };
      }

      // Si no está en system_users, buscar en clients
      final clientResponse = await supabase
          .from('clients')
          .select('*')
          .eq('email', user.email!)
          .maybeSingle();

      if (clientResponse != null) {
        return {
          'id': clientResponse['id'],
          'email': clientResponse['email'],
          'first_name': clientResponse['first_name'],
          'last_name': clientResponse['last_name'],
          'phone': clientResponse['phone'],
          'qr_code': clientResponse['qr_code'],
          'is_active': clientResponse['is_active'],
          'role': roleClient,
          'user_type': 'client',
        };
      }

      // Usuario autenticado pero sin perfil en las tablas
      return {
        'id': null,
        'email': user.email,
        'first_name': null,
        'last_name': null,
        'phone': null,
        'role': roleClient, // Por defecto cliente
        'user_type': 'unknown',
        'is_active': true,
      };
    } catch (error) {
      if (kDebugMode) {
        print('Error obteniendo información del usuario: $error');
      }
      rethrow;
    }
  }

  // Obtener pestañas disponibles para el usuario actual
  Future<List<NavigationTab>> getAvailableTabs() async {
    final userInfo = await getCurrentUserInfo();
    final role = userInfo['role'] ?? roleClient;
    return getAvailableTabsForRole(role);
  }

  // Obtener pestañas disponibles según el rol
  List<NavigationTab> getAvailableTabsForRole(String role) {
    switch (role) {
      case roleAdmin:
        return [
          NavigationTab.qr,
          NavigationTab.subscriptions,
          NavigationTab.store,
          NavigationTab.dashboard,
          NavigationTab.users, // Solo admin puede ver usuarios
        ];
      case roleManager:
        return [
          NavigationTab.qr,
          NavigationTab.subscriptions,
          NavigationTab.store,
          NavigationTab.dashboard,
        ];
      case roleReceptionist:
        return [
          NavigationTab.qr,
          NavigationTab.subscriptions,
        ];
      case roleClient:
      default:
        return [
          NavigationTab.scanner, // Para escanear QR de entrada/salida
          NavigationTab.profile, // Ver su perfil y suscripción
        ];
    }
  }

  // Verificar si el usuario tiene permiso para una acción específica
  bool hasPermission(String role, String permission) {
    switch (role) {
      case roleAdmin:
        return true; // Admin tiene todos los permisos
      case roleManager:
        return [
          'view_dashboard',
          'manage_subscriptions',
          'manage_store',
          'scan_qr',
        ].contains(permission);
      case roleReceptionist:
        return [
          'scan_qr',
          'view_subscriptions',
        ].contains(permission);
      case roleClient:
        return [
          'scan_own_qr',
          'view_own_profile',
        ].contains(permission);
      default:
        return false;
    }
  }
}