# Configuración de Supabase para GymTracker

Este documento explica cómo configurar las tablas de Supabase para el sistema de gimnasio GymTracker.

## 📋 Resumen del Sistema

El sistema está diseñado para:
- Gestionar diferentes tipos de suscripciones
- Registrar clientes con códigos QR únicos
- Hacer seguimiento en tiempo real de quién está en el gimnasio
- Controlar acceso según tipo de suscripción y horarios
- Limitar clases para suscripciones específicas

## 🗃️ Estructura de Tablas

### 1. `subscription_types` - Tipos de Suscripciones
Contiene los 4 tipos de suscripciones mostradas en la imagen:
- **Mensualidad**: $1,400 - Acceso completo
- **Matutino**: $1,200 - Solo mañanas (6:00 AM - 10:00 AM)
- **Vespertino**: $1,200 - Solo tardes (2:00 PM - 6:00 PM, L-V)
- **12 clases al mes**: $1,000 - Límite de 12 clases

### 2. `clients` - Clientes
Información de los miembros del gimnasio:
- Datos personales
- Código QR único para acceso
- Contacto de emergencia

### 3. `client_subscriptions` - Suscripciones Activas
Relación entre clientes y sus suscripciones:
- Fechas de inicio y fin
- Contador de clases usadas
- Estado activo/inactivo

### 4. `gym_visits` - Visitas en Tiempo Real
Registro de entradas y salidas:
- Check-in y check-out
- Estado actual (dentro/fuera)
- Tipo de visita

## 🚀 Instalación

### Paso 1: Ejecutar el Script SQL
1. Ve a tu proyecto en Supabase Dashboard
2. Navega a "SQL Editor"
3. Copia y pega el contenido completo de `supabase_schema.sql`
4. Ejecuta el script

### Paso 2: Verificar Tablas
Después de ejecutar el script, deberías ver:
- 4 tablas principales creadas
- Datos de suscripciones insertados
- Índices para optimización
- Triggers y funciones configurados
- Vista para clientes en tiempo real

### Paso 3: Configurar Variables de Entorno
Asegúrate de tener un archivo `.env` en la raíz del proyecto:
```
SUPABASE_URL=tu_url_de_supabase
SUPABASE_ANON_KEY=tu_clave_anonima
```

## 🔧 Funcionalidades Implementadas

### ✅ Sistema de Suscripciones
- Carga automática desde Supabase
- Interfaz visual similar a la imagen
- Validación de horarios automática
- Control de límite de clases

### ✅ Validaciones Automáticas
- **Horarios**: Los triggers validan automáticamente si un cliente puede acceder según su suscripción
- **Clases**: Se incrementa automáticamente el contador de clases usadas
- **Tiempo Real**: RLS habilitado para actualizaciones en vivo

### ✅ Vista en Tiempo Real
La vista `clients_currently_in_gym` muestra:
- Quién está actualmente en el gimnasio
- Hora de entrada
- Tipo de suscripción

## 📱 Uso en la App

### Página de Suscripciones
- Muestra las 4 suscripciones en formato de tarjetas
- Diseño oscuro similar a la imagen
- Botón "Elegir" para seleccionar suscripción
- Carga datos directamente de Supabase

### Próximas Funcionalidades
1. **Gestión de Clientes**: Agregar, editar, buscar clientes
2. **Asignación de Suscripciones**: Conectar clientes con suscripciones
3. **Sistema QR**: Escaneo para check-in/check-out
4. **Dashboard en Tiempo Real**: Ver quién está en el gimnasio
5. **Reportes**: Estadísticas de uso y ingresos

## 🔒 Seguridad

### Row Level Security (RLS)
- Habilitado en todas las tablas
- Políticas básicas configuradas
- **Importante**: Ajustar políticas según sistema de autenticación

### Recomendaciones
1. Implementar autenticación de usuarios (staff del gimnasio)
2. Crear políticas RLS específicas por rol
3. Configurar backup automático
4. Monitorear uso de la base de datos

## 🔄 Tiempo Real

El sistema está configurado para funcionar en tiempo real:
- Cambios en `gym_visits` se reflejan inmediatamente
- La vista `clients_currently_in_gym` se actualiza automáticamente
- Supabase Realtime habilitado en todas las tablas

## 📊 Consultas Útiles

### Ver clientes actualmente en el gimnasio:
```sql
SELECT * FROM clients_currently_in_gym;
```

### Verificar suscripciones activas:
```sql
SELECT c.first_name, c.last_name, st.name, cs.end_date
FROM clients c
JOIN client_subscriptions cs ON c.id = cs.client_id
JOIN subscription_types st ON cs.subscription_type_id = st.id
WHERE cs.is_active = true AND cs.end_date >= CURRENT_DATE;
```

### Estadísticas de uso por suscripción:
```sql
SELECT st.name, COUNT(*) as total_subscriptions
FROM client_subscriptions cs
JOIN subscription_types st ON cs.subscription_type_id = st.id
WHERE cs.is_active = true
GROUP BY st.name;
```

## 🐛 Troubleshooting

### Error: "relation does not exist"
- Verifica que el script SQL se ejecutó completamente
- Revisa que estás conectado al proyecto correcto

### Error de conexión a Supabase
- Verifica las variables de entorno
- Confirma que las credenciales son correctas

### Problemas de tiempo real
- Verifica que RLS está habilitado
- Confirma que las políticas permiten las operaciones necesarias

## 📞 Soporte

Para problemas o dudas:
1. Revisa los logs de Supabase Dashboard
2. Verifica la consola de Flutter para errores
3. Consulta la documentación de Supabase Realtime

---

**Nota**: Este sistema está diseñado para ser escalable y fácil de mantener. Las validaciones automáticas y el tiempo real aseguran una experiencia fluida para el personal del gimnasio.