import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants.dart';
import '../services/user_service.dart';
import 'qr_page.dart';
import 'subscriptions_page.dart';
import 'store_page.dart';
import 'dashboard_page.dart';
import 'client_profile_page.dart';
import 'client_qr_scanner_page.dart';
import 'users_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final supabase = Supabase.instance.client;
  final UserService _userService = UserService();
  int _selectedIndex = 0;
  bool _isLoading = true;
  List<NavigationTab> _availableTabs = [];
  String? _userRole;
  String? _userEmail;

  final Map<NavigationTab, Widget> _pageMap = {
    NavigationTab.qr: const QRPage(),
    NavigationTab.subscriptions: const SubscriptionsPage(),
    NavigationTab.store: const StorePage(),
    NavigationTab.dashboard: const DashboardPage(),
    NavigationTab.users: const UsersPage(),
    NavigationTab.profile: const ClientProfilePage(),
    NavigationTab.scanner: const ClientQRScannerPage(),
  };

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Usuario no autenticado');
      }

      _userEmail = user.email;
      final userInfo = await _userService.getCurrentUserInfo();
      _userRole = userInfo['role'];
      _availableTabs = await _userService.getAvailableTabs();

      setState(() {
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al cargar datos del usuario: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onItemTapped(int index) {
    if (index < _availableTabs.length) {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  Widget _getCurrentPage() {
    if (_availableTabs.isEmpty || _selectedIndex >= _availableTabs.length) {
      return const Center(
        child: Text('No hay páginas disponibles'),
      );
    }
    
    final currentTab = _availableTabs[_selectedIndex];
    return _pageMap[currentTab] ?? const Center(
      child: Text('Página no encontrada'),
    );
  }

  Future<void> _signOut() async {
    try {
      await supabase.auth.signOut();
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al cerrar sesión: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Cerrar Sesión'),
          content: const Text('¿Estás seguro de que quieres cerrar sesión?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancelar'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _signOut();
              },
              child: const Text(
                'Cerrar Sesión',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Image.asset(
              'assets/images/logo.jpg',
              height: 32,
            ),
            const SizedBox(width: 8),
            const Text(AppConstants.appTitle),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'logout') {
                _showSignOutDialog();
              }
            },
            itemBuilder: (BuildContext context) => [
              PopupMenuItem<String>(
                enabled: false,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _userEmail ?? 'Usuario',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      _userRole?.toUpperCase() ?? 'USUARIO',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem<String>(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Cerrar Sesión'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _getCurrentPage(),
      bottomNavigationBar: _availableTabs.isNotEmpty
          ? BottomNavigationBar(
              type: BottomNavigationBarType.fixed,
              items: _availableTabs.map((tab) {
                return BottomNavigationBarItem(
                  icon: Icon(tab.iconData),
                  label: tab.label,
                );
              }).toList(),
              currentIndex: _selectedIndex,
              onTap: _onItemTapped,
            )
          : null,
    );
  }
}