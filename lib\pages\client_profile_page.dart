import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ClientProfilePage extends StatefulWidget {
  const ClientProfilePage({super.key});

  @override
  State<ClientProfilePage> createState() => _ClientProfilePageState();
}

class _ClientProfilePageState extends State<ClientProfilePage> {
  final supabase = Supabase.instance.client;
  bool _isLoading = true;
  Map<String, dynamic>? _clientData;
  Map<String, dynamic>? _currentSubscription;
  List<Map<String, dynamic>> _recentVisits = [];

  @override
  void initState() {
    super.initState();
    _loadClientData();
  }

  Future<void> _loadClientData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('Usuario no autenticado');
      }

      // Obtener datos del cliente
      final clientResponse = await supabase
          .from('clients')
          .select('*')
          .eq('email', user.email!)
          .single();

      // Obtener suscripción activa
      final subscriptionResponse = await supabase
          .from('client_subscriptions')
          .select('''
            *,
            subscription_types (
              name,
              price,
              duration_months,
              description,
              schedule_restrictions,
              class_limit
            )
          ''')
          .eq('client_id', clientResponse['id'])
          .eq('is_active', true)
          .gte('end_date', DateTime.now().toIso8601String().split('T')[0])
          .maybeSingle();

      // Obtener visitas recientes
      final visitsResponse = await supabase
          .from('gym_visits')
          .select('check_in_time, check_out_time, is_currently_inside')
          .eq('client_id', clientResponse['id'])
          .order('check_in_time', ascending: false)
          .limit(10);

      setState(() {
        _clientData = clientResponse;
        _currentSubscription = subscriptionResponse;
        _recentVisits = List<Map<String, dynamic>>.from(visitsResponse);
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al cargar datos: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _loadClientData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Información personal
              _buildPersonalInfoCard(),
              const SizedBox(height: 16),
              
              // Suscripción actual
              _buildSubscriptionCard(),
              const SizedBox(height: 16),
              
              // Historial de visitas
              _buildVisitsHistoryCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalInfoCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).primaryColor,
                  child: Text(
                    (_clientData?['first_name']?.substring(0, 1).toUpperCase() ?? 'U'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${_clientData?['first_name'] ?? ''} ${_clientData?['last_name'] ?? ''}',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _clientData?['email'] ?? '',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            _buildInfoRow('Teléfono', _clientData?['phone'] ?? 'No registrado'),
            _buildInfoRow('Fecha de nacimiento', _clientData?['date_of_birth'] ?? 'No registrada'),
            _buildInfoRow('Contacto de emergencia', _clientData?['emergency_contact_name'] ?? 'No registrado'),
            _buildInfoRow('Teléfono de emergencia', _clientData?['emergency_contact_phone'] ?? 'No registrado'),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionCard() {
    if (_currentSubscription == null) {
      return Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(
                Icons.warning_amber,
                size: 48,
                color: Colors.orange[600],
              ),
              const SizedBox(height: 16),
              Text(
                'Sin Suscripción Activa',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'No tienes una suscripción activa. Contacta a recepción para adquirir una.',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    final subscription = _currentSubscription!;
    final subscriptionType = subscription['subscription_types'];
    final startDate = DateTime.parse(subscription['start_date']);
    final endDate = DateTime.parse(subscription['end_date']);
    final daysRemaining = endDate.difference(DateTime.now()).inDays;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.card_membership,
                  color: Theme.of(context).primaryColor,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Suscripción Activa',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: daysRemaining > 7 ? Colors.green : Colors.orange,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '$daysRemaining días',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              subscriptionType['name'],
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subscriptionType['description'] ?? '',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            _buildInfoRow('Precio', '\$${subscriptionType['price']}'),
            _buildInfoRow('Inicio', _formatDate(startDate)),
            _buildInfoRow('Vencimiento', _formatDate(endDate)),
            if (subscriptionType['class_limit'] != null)
              _buildInfoRow(
                'Clases usadas',
                '${subscription['classes_used']}/${subscriptionType['class_limit']}',
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildVisitsHistoryCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: Theme.of(context).primaryColor,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Text(
                  'Historial de Visitas',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_recentVisits.isEmpty)
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.fitness_center,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No hay visitas registradas',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _recentVisits.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final visit = _recentVisits[index];
                  final checkIn = DateTime.parse(visit['check_in_time']);
                  final checkOut = visit['check_out_time'] != null
                      ? DateTime.parse(visit['check_out_time'])
                      : null;
                  final isCurrentlyInside = visit['is_currently_inside'] ?? false;

                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: isCurrentlyInside ? Colors.green : Colors.grey,
                      child: Icon(
                        isCurrentlyInside ? Icons.fitness_center : Icons.check,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(
                      _formatDateTime(checkIn),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text(
                      checkOut != null
                          ? 'Salida: ${_formatDateTime(checkOut)}'
                          : isCurrentlyInside
                              ? 'Actualmente en el gimnasio'
                              : 'Sin registro de salida',
                    ),
                    trailing: Icon(
                      isCurrentlyInside ? Icons.circle : Icons.check_circle,
                      color: isCurrentlyInside ? Colors.green : Colors.grey,
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}